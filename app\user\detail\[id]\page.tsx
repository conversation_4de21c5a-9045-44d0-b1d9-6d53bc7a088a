﻿"use client"

import React, { useState, useEffect } from "react"
import { useRout<PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { use } from "react"
import { 
  ArrowLeft, 
  Edit,
  Loader2, 
  User as UserIcon, 
  Users,
  Mail, 
  AlertCircle,
  Settings,
  Bell,
  Lock,
  Shield
} from "lucide-react"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getUserByIdRequest } from "@/services/api/userRequestApi"
import { User } from "@/types/user"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

// 导入用户详情组件
import { 
  UserBasicInfo, 
  UserRoles, 
  UserActivity
} from "@/components/pages/user-detail"

interface UserDetailPageProps {
  params: Promise<{ id: string }> | { id: string };
}

export default function UserDetailPage({ params }: UserDetailPageProps) {
  const router = useRouter()
  
  // 使用React.use解包params
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const userId = resolvedParams.id;
  
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<User | null>(null)
  const [activeTab, setActiveTab] = useState("overview")
  
  // 初始化加载数据
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const userData = await getUserByIdRequest(parseInt(userId, 10))
        if (userData) {
          setUser(userData)
        } else {
          setError("未找到用户信息")
        }
      } catch (err) {
        console.error("加载用户数据失败", err)
        setError("加载用户数据失败，请稍后重试")
      } finally {
        setIsLoading(false)
      }
    }
    
    loadData()
  }, [userId, router])
  
  // 面包屑
  const breadcrumbItems = [
    { label: "设置", href: "/settings" },
    { label: "用户管理", href: "/settings/users" },
    { label: user?.nickname || "用户详情", active: true }
  ]
  
  // 加载状态
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <HeaderWithBreadcrumb items={breadcrumbItems}/>
        <main className="flex-1">
          <div className="container mx-auto px-6 py-6 max-w-7xl">
            <div className="flex justify-center items-center h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          </div>
        </main>
      </div>
    )
  }
  
  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems}/>
      <main className="flex-1">
        <div className="container mx-auto px-6 py-6 max-w-7xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => router.push("/settings/users")}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">用户详情</h1>
                <p className="text-sm text-muted-foreground">查看和管理用户信息</p>
              </div>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => router.push(`/settings/users`)}
              >
                <Users className="h-4 w-4" />
                <span>所有用户</span>
              </Button>
              <Button
                onClick={() => router.push(`/user/detail/${userId}/edit`)}
                className="gap-2"
              >
                <Edit className="h-4 w-4" />
                <span>编辑用户</span>
              </Button>
            </div>
          </div>
          
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>错误</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧：用户基本信息 - 缩小尺寸 */}
            <div className="col-span-1">
              <div className="max-w-md">
                <UserBasicInfo user={user} />
              </div>
            </div>
            
            {/* 右侧：详细信息选项卡 */}
            <div className="col-span-1 lg:col-span-2">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="border-b border-border/40 mb-4">
                  <TabsList className="bg-transparent h-auto p-0 mb-0">
                    <TabsTrigger value="overview" className="rounded-none relative pb-2 pt-2 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-0">
                      概览
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary transition-transform data-[state=active]:scale-100 scale-0" />
                    </TabsTrigger>
                    <TabsTrigger value="roles" className="rounded-none relative pb-2 pt-2 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-0">
                      角色
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary transition-transform data-[state=active]:scale-100 scale-0" />
                    </TabsTrigger>
                    <TabsTrigger value="settings" className="rounded-none relative pb-2 pt-2 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-0">
                      设置
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary transition-transform data-[state=active]:scale-100 scale-0" />
                    </TabsTrigger>
                </TabsList>
                </div>
                
                {/* 概览选项卡内容 */}
                <TabsContent value="overview" className="space-y-6 mt-0">
                  <div className="grid grid-cols-1 gap-6">
                    <UserActivity userId={parseInt(userId)} />
                  </div>
                </TabsContent>
                
                {/* 角色选项卡内容 */}
                <TabsContent value="roles" className="space-y-6 mt-0">
                  <UserRoles userId={parseInt(userId)} />
                </TabsContent>
                
                {/* 设置选项卡内容 */}
                <TabsContent value="settings" className="space-y-6 mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Settings className="h-5 w-5 text-primary" />
                        用户设置
                      </CardTitle>
                      <CardDescription>管理用户的系统设置和偏好</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">通知设置</Label>
                            <p className="text-sm text-muted-foreground">
                              控制系统通知的接收方式
                            </p>
                          </div>
                          <Button variant="outline" size="sm" className="gap-2">
                            <Bell className="h-4 w-4" />
                            配置通知
                          </Button>
                        </div>
                        
                        <Separator />
                        
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">启用双因素认证</Label>
                            <p className="text-sm text-muted-foreground">
                              提高账户安全性，使用双重验证
                            </p>
                          </div>
                          <Switch />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">登录通知</Label>
                            <p className="text-sm text-muted-foreground">
                              账户登录时发送邮件通知
                            </p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                        
                        <Separator />
                        
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">安全设置</Label>
                            <p className="text-sm text-muted-foreground">
                              管理密码和安全选项
                            </p>
                          </div>
                          <Button variant="outline" size="sm" className="gap-2">
                            <Lock className="h-4 w-4" />
                            安全设置
                          </Button>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">访问权限</Label>
                            <p className="text-sm text-muted-foreground">
                              管理用户的系统访问权限
                            </p>
                          </div>
                          <Button variant="outline" size="sm" className="gap-2">
                            <Shield className="h-4 w-4" />
                            权限设置
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
} 