"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"

interface NavigationContextType {
  isPageTransition: boolean
  shouldShowTransition: boolean
  visitedPaths: Set<string>
}

const NavigationContext = createContext<NavigationContextType>({
  isPageTransition: false,
  shouldShowTransition: true,
  visitedPaths: new Set(),
})

export function useNavigation() {
  return useContext(NavigationContext)
}

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isPageTransition, setIsPageTransition] = useState(false)
  const [shouldShowTransition, setShouldShowTransition] = useState(true)
  const [visitedPaths, setVisitedPaths] = useState(new Set<string>())
  const [previousPath, setPreviousPath] = useState<string>("")
  const [isInitialized, setIsInitialized] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const currentPath = pathname + searchParams.toString()

  // 初始化当前路径
  useEffect(() => {
    if (!isInitialized) {
      setVisitedPaths(prev => {
        const newSet = new Set(prev)
        newSet.add(currentPath)
        return newSet
      })
      setPreviousPath(currentPath)
      setIsInitialized(true)
    }
  }, [currentPath, isInitialized])

  // 监听路由变化，设置页面过渡状态
  useEffect(() => {
    if (isInitialized && previousPath && previousPath !== currentPath) {
      setIsPageTransition(true)

      // 300ms后结束过渡并记录路径
      const timer = setTimeout(() => {
        setIsPageTransition(false)
        setVisitedPaths(prev => {
          const newSet = new Set(prev)
          newSet.add(currentPath)
          return newSet
        })
        setPreviousPath(currentPath)
      }, 300)

      return () => clearTimeout(timer)
    }
  }, [currentPath, previousPath, isInitialized])
  
  // 用户可以通过设置localStorage禁用页面过渡效果
  useEffect(() => {
    const disableTransition = localStorage.getItem("disable-page-transition") === "true"
    setShouldShowTransition(!disableTransition)
    
    const handleStorageChange = () => {
      const disableTransition = localStorage.getItem("disable-page-transition") === "true"
      setShouldShowTransition(!disableTransition)
    }
    
    window.addEventListener("storage", handleStorageChange)
    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])
  
  return (
    <NavigationContext.Provider 
      value={{ 
        isPageTransition, 
        shouldShowTransition,
        visitedPaths
      }}
    >
      {children}
    </NavigationContext.Provider>
  )
} 