"use client"

import { useState, useEffect, use<PERSON>em<PERSON> } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle, Info, FileUp, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { IconSelector } from "@/components/common-custom/icon-selector"
import { BackButton } from "@/components/common-custom/back-button"
import { HeaderWithBreadcrumb, type BreadcrumbItem } from "@/components/project-custom/breadcrumb"
import { getTeamOptionsRequest, createTeamRequest, checkTeamCodeExists } from "@/services/api/teamRequestApi"
import { ScrollArea } from "@/components/ui/scroll-area"

// 生成随机团队代码
const generateTeamCode = () => {
  const prefix = 'TEAM';
  const randomPart = Math.random().toString(36).substring(2, 7).toUpperCase();
  const timestamp = new Date().getTime().toString().slice(-4);
  return `${prefix}-${randomPart}-${timestamp}`;
}

// 颜色代码映射
const colorCodeMap: Record<string, string> = {
  "black": "#000000",
  "default": "#6366f1",
  "gray": "#6B7280",
  "red": "#f43f5e",
  "green": "#10b981",
  "blue": "#3b82f6",
  "purple": "#8b5cf6",
  "orange": "#f97316",
  "indigo": "#4f46e5",
  "pink": "#EC4899",
  "amber": "#F59E0B"
};

export default function CreateTeamPage() {
  const [parentTeam, setParentTeam] = useState<string>("0")
  const [teamName, setTeamName] = useState("")
  const [teamCode, setTeamCode] = useState(() => generateTeamCode())
  const [teamDescription, setTeamDescription] = useState("")
  const [isPrivate, setIsPrivate] = useState(false)
  const [teamColor, setTeamColor] = useState("black")
  const [teamStatus, setTeamStatus] = useState<"启用" | "停用">("启用")
  const [icon, setIcon] = useState<string>("Building")
  const [avatar, setAvatar] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [teamOptions, setTeamOptions] = useState<Array<{id: number; label: string; value: string}>>([])
  const [isLoading, setIsLoading] = useState(true)
  const [checkingCode, setCheckingCode] = useState(false)
  const [teamCodeExists, setTeamCodeExists] = useState(false)
  const [errors, setErrors] = useState<{
    teamName?: string;
    teamCode?: string;
    teamDescription?: string;
    teamColor?: string;
    icon?: string;
    isPrivate?: string;
    server?: string;
  }>({})
  const router = useRouter()

  // 为上传的图片创建URL
  const avatarUrl = useMemo(() => {
    if (avatar) {
      return URL.createObjectURL(avatar);
    }
    return null;
  }, [avatar]);

  // 获取团队选项数据
  useEffect(() => {
    const fetchTeamOptions = async () => {
      try {
        const options = await getTeamOptionsRequest();
        // 添加默认选项
        const allOptions = [
          { id: 0, label: "无上级团队（顶级团队）", value: "0" },
          ...options
        ];
        setTeamOptions(allOptions);
      } catch (error) {
        console.error("获取团队选项失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamOptions();
  }, []);

  // 清除URL对象，防止内存泄漏
  useEffect(() => {
    return () => {
      if (avatarUrl) {
        URL.revokeObjectURL(avatarUrl);
      }
    };
  }, [avatarUrl]);

  // 获取当前选择的父团队级别
  const getParentLevel = () => {
    const parent = teamOptions.find(team => team.value === parentTeam);
    return parent ? 1 : 0;
  }

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setAvatar(event.target.files[0]);
    }
  }

  // 页面标题显示
  const pageTitle = useMemo(() => {
    if (!teamName.trim()) return "创建团队";
    return `${teamName.trim()}`;
  }, [teamName]);

  // 面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "首页", href: "/" },
    { label: "团队管理", href: "/teams" },
    { label: "创建团队", isCurrent: true }
  ]

  // 验证表单
  const validateForm = () => {
    const newErrors: typeof errors = {}

    if (!teamName.trim()) {
      newErrors.teamName = "团队名称不能为空"
    } else if (teamName.length > 50) {
      newErrors.teamName = "团队名称不能超过50个字符"
    }

    if (!teamCode.trim()) {
      newErrors.teamCode = "团队代码不能为空"
    } else if (teamCode.length > 30) {
      newErrors.teamCode = "团队代码不能超过30个字符"
    } else if (teamCodeExists) {
      newErrors.teamCode = "团队代码已存在，请更换"
    }

    if (teamDescription && teamDescription.length > 200) {
      newErrors.teamDescription = "团队描述不能超过200个字符"
    }
    
    // 验证外观和权限部分必填项
    if (!teamColor) {
      newErrors.teamColor = "请选择团队主题色"
    }
    
    if (!icon) {
      newErrors.icon = "请选择团队图标"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 检查团队代码是否已存在
  const checkTeamCode = async (code: string) => {
    if (!code.trim()) return;

    setCheckingCode(true);
    try {
      const exists = await checkTeamCodeExists(code);
      setTeamCodeExists(exists);

      if (exists) {
        setErrors(prev => ({
          ...prev,
          teamCode: "团队代码已存在，请更换"
        }));
      } else {
        setErrors(prev => ({
          ...prev,
          teamCode: undefined
        }));
      }
    } catch (error) {
      console.error("检查团队代码失败:", error);
    } finally {
      setCheckingCode(false);
    }
  }

  const handleCreateTeam = async () => {
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      // 构建请求参数
      const teamData = {
        teamCode: teamCode,
        teamName: teamName.trim(),
        teamLogo: avatarUrl || `lucide-${icon.toLowerCase()}`,
        teamThemeColor: colorCodeMap[teamColor],
        teamType: "1", // 默认类型 - 部门
        description: teamDescription.trim(),
        privateTag: isPrivate ? 1 : 0,
        parentCode: parentTeam === "0" ? null : parentTeam,
        status: teamStatus === "启用" ? 1 : 0  // 添加状态字段
      }

      // 调用API创建团队
      const response = await createTeamRequest(teamData)

      // 修改响应处理逻辑，无论返回什么类型，只要不为null即视为成功
      if (response !== null) {
        setShowSuccess(true)

        // 显示成功消息1.5秒后重定向
        setTimeout(() => {
          router.push("/teams")
        }, 1500)
      }
    } catch (error) {
      console.error("创建团队失败:", error)
      setErrors(prev => ({
        ...prev,
        server: "创建团队失败，请稍后重试"
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
      <div className="flex flex-col min-h-screen w-full bg-background">
        <HeaderWithBreadcrumb items={breadcrumbItems} />
        <div className="flex-1">
          <div className="container px-4 py-6 mx-auto max-w-5xl">
            {showSuccess ? (
                <div className="bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-900 rounded-lg p-8 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="rounded-full bg-green-100 dark:bg-green-900/50 p-4 mb-4">
                      <CheckCircle className="h-10 w-10 text-green-600 dark:text-green-400" strokeWidth={1.5} />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">团队创建成功！</h3>
                    <p className="text-muted-foreground text-center max-w-md">
                      您的新团队已成功创建，正在为您跳转到团队页面...
                    </p>
                  </div>
                </div>
            ) : (
                <div className="bg-background">
                  {errors.server && (
                      <Alert variant="destructive" className="mb-6">
                        <AlertDescription className="flex items-center">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          {errors.server}
                        </AlertDescription>
                      </Alert>
                  )}

                  {/* 头部标题和操作按钮 */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center gap-3">
                      <BackButton
                        href="/teams"
                        onNavigate={(href) => router.push(href)}
                      />
                      <h1 className="text-2xl font-semibold tracking-tight">{pageTitle}</h1>
                    </div>
                    <div className="flex gap-3">
                      <Button
                          type="button"
                          variant="outline"
                          asChild
                          className="w-24"
                      >
                        <Link href="/teams">取消</Link>
                      </Button>
                      <Button
                          type="submit"
                          form="create-team-form"
                          disabled={!teamName || isSubmitting}
                          className="w-24"
                      >
                        {isSubmitting ? (
                            <>
                              <div className="mr-2 h-3.5 w-3.5 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                              创建中...
                            </>
                        ) : "创建团队"}
                      </Button>
                    </div>
                  </div>

                  <form id="create-team-form" className="space-y-8" onSubmit={(e) => { e.preventDefault(); handleCreateTeam(); }}>
                    {/* 基本信息部分 */}
                    <div>
                      <h2 className="text-lg font-medium border-b pb-2 mb-4">基本信息</h2>
                      <div className="space-y-4 pl-1">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                          <div className="space-y-1.5">
                            <Label htmlFor="team-name" className="text-sm font-medium">
                              团队名称
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <Input
                                id="team-name"
                                placeholder="输入团队名称"
                                value={teamName}
                                onChange={(e) => {
                                  setTeamName(e.target.value)
                                  if (errors.teamName) {
                                    setErrors(prev => ({ ...prev, teamName: undefined }))
                                  }
                                }}
                                className={`h-9 ${errors.teamName ? 'border-destructive focus-visible:ring-destructive/20' : ''}`}
                                required
                            />
                            {errors.teamName && (
                                <p className="text-xs text-destructive mt-1 flex items-center">
                                  <AlertCircle className="h-3 w-3 mr-1" />
                                  {errors.teamName}
                                </p>
                            )}
                          </div>

                          <div className="space-y-1.5">
                            <Label htmlFor="parent-team" className="text-sm font-medium">上级团队</Label>
                            <Select value={parentTeam} onValueChange={setParentTeam} disabled={isLoading}>
                              <SelectTrigger className="h-9">
                                <SelectValue placeholder={isLoading ? "加载中..." : "选择上级团队（可选）"} />
                              </SelectTrigger>
                              <SelectContent>
                                <ScrollArea className="max-h-[240px]">
                                  {teamOptions.map(team => (
                                      <SelectItem
                                          key={team.value}
                                          value={team.value}
                                      >
                                        {team.label}
                                      </SelectItem>
                                  ))}
                                </ScrollArea>
                              </SelectContent>
                            </Select>
                            {getParentLevel() >= 3 && (
                                <p className="text-xs text-amber-500 mt-1 flex items-center">
                                  <Info className="h-3 w-3 mr-1" />
                                  注意：您正在创建深层级子团队，确保团队结构不会过于复杂
                                </p>
                            )}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                          <div className="space-y-1.5">
                            <Label htmlFor="team-code" className="text-sm font-medium">
                              团队代码
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <div className="flex gap-2 relative">
                              <div className="flex-1 relative">
                                <Input
                                    id="team-code"
                                    placeholder="输入团队代码"
                                    value={teamCode}
                                    onChange={(e) => {
                                      setTeamCode(e.target.value);
                                      setTeamCodeExists(false);
                                      if (errors.teamCode) {
                                        setErrors(prev => ({ ...prev, teamCode: undefined }));
                                      }
                                    }}
                                    onBlur={(e) => checkTeamCode(e.target.value)}
                                    className={`h-9 ${errors.teamCode ? 'border-destructive focus-visible:ring-destructive/20' : ''}`}
                                    required
                                />
                                {checkingCode && (
                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                      <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                                    </div>
                                )}
                              </div>
                              <Button
                                  type="button"
                                  variant="outline"
                                  onClick={() => {
                                    const newCode = generateTeamCode();
                                    setTeamCode(newCode);
                                    checkTeamCode(newCode);
                                  }}
                                  className="h-9 px-3 whitespace-nowrap"
                              >
                                自动生成
                              </Button>
                            </div>
                            {errors.teamCode && (
                                <p className="text-xs text-destructive mt-1 flex items-center">
                                  <AlertCircle className="h-3 w-3 mr-1" />
                                  {errors.teamCode}
                                </p>
                            )}
                            {teamCodeExists && !errors.teamCode && (
                                <p className="text-xs text-amber-500 mt-1 flex items-center">
                                  <Info className="h-3 w-3 mr-1" />
                                  该团队代码已存在，建议更换
                                </p>
                            )}
                          </div>

                          <div className="space-y-1.5">
                            <Label htmlFor="team-status" className="text-sm font-medium">
                              团队状态
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <div className="flex items-center gap-2 mt-1">
                              <Button
                                  type="button"
                                  variant={teamStatus === "启用" ? "default" : "outline"}
                                  onClick={() => setTeamStatus("启用")}
                                  className="h-9 px-3"
                              >
                                <CheckCircle className={`h-4 w-4 mr-1.5 ${teamStatus === "启用" ? "" : "text-muted-foreground"}`} />
                                启用
                              </Button>
                              <Button
                                  type="button"
                                  variant={teamStatus === "停用" ? "default" : "outline"}
                                  onClick={() => setTeamStatus("停用")}
                                  className="h-9 px-3"
                              >
                                <AlertCircle className={`h-4 w-4 mr-1.5 ${teamStatus === "停用" ? "" : "text-muted-foreground"}`} />
                                停用
                              </Button>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-1.5">
                          <Label htmlFor="team-description" className="text-sm font-medium">团队描述</Label>
                          <Textarea
                              id="team-description"
                              placeholder="输入团队描述（团队的主要职责、业务范围等）"
                              value={teamDescription}
                              onChange={(e) => {
                                setTeamDescription(e.target.value)
                                if (errors.teamDescription) {
                                  setErrors(prev => ({ ...prev, teamDescription: undefined }))
                                }
                              }}
                              rows={2}
                              className={`resize-none ${errors.teamDescription ? 'border-destructive focus-visible:ring-destructive/20' : ''}`}
                          />
                          <div className="flex justify-between">
                            {errors.teamDescription && (
                                <p className="text-xs text-destructive mt-1 flex items-center">
                                  <AlertCircle className="h-3 w-3 mr-1" />
                                  {errors.teamDescription}
                                </p>
                            )}
                            <p className="text-xs text-muted-foreground text-right ml-auto">
                              {teamDescription.length}/200
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 外观与权限部分 - 去除分割线 */}
                    <div>
                      <h2 className="text-lg font-medium border-b pb-2 mb-4">外观与权限</h2>
                      <div className="space-y-6 pl-1">
                        <div className="space-y-2">
                          <Label htmlFor="team-color" className="text-sm font-medium">
                            团队主题色
                            <span className="text-destructive ml-1">*</span>
                          </Label>
                          <p className="text-xs text-muted-foreground mt-0.5">选择一个代表团队的颜色</p>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {["black", "blue", "green", "red", "purple", "orange", "indigo", "pink", "amber", "gray"].map(color => (
                              <TooltipProvider key={color}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <button
                                      type="button"
                                      className={[
                                        'w-7 h-7 rounded-full transition-all focus:outline-none flex-shrink-0',
                                        teamColor === color ? 'scale-110 ring-2 ring-offset-1 ring-primary' : 'opacity-50 hover:opacity-80',
                                        color === 'black' ? `bg-black ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                          color === 'blue' ? `bg-blue-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                            color === 'green' ? `bg-green-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                              color === 'red' ? `bg-red-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                                color === 'purple' ? `bg-purple-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                                  color === 'orange' ? `bg-orange-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                                    color === 'indigo' ? `bg-indigo-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                                      color === 'pink' ? `bg-pink-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                                        color === 'amber' ? `bg-amber-500 ${teamColor !== color ? 'bg-opacity-70' : ''}` :
                                                          `bg-gray-500 ${teamColor !== color ? 'bg-opacity-70' : ''}`
                                      ].join(' ')}
                                      onClick={() => {
                                        setTeamColor(color)
                                        if (errors.teamColor) {
                                          setErrors(prev => ({ ...prev, teamColor: undefined }))
                                        }
                                      }}
                                      aria-label={`选择${color}颜色`}
                                    />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="capitalize">{
                                      color === "black" ? "黑色" :
                                        color === "blue" ? "蓝色" :
                                          color === "green" ? "绿色" :
                                            color === "red" ? "红色" :
                                              color === "purple" ? "紫色" :
                                                color === "orange" ? "橙色" :
                                                  color === "indigo" ? "靛蓝色" :
                                                    color === "pink" ? "粉色" :
                                                      color === "amber" ? "琥珀色" :
                                                        "灰色"
                                    }</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            ))}
                          </div>
                          {errors.teamColor && (
                            <p className="text-xs text-destructive flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              {errors.teamColor}
                            </p>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="team-icon" className="text-sm font-medium">
                              团队图标
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <div className="flex items-start gap-3">
                              <div className="w-12 h-12 flex items-center justify-center overflow-hidden rounded-md bg-muted/30">
                                {avatarUrl ? (
                                    <img
                                        src={avatarUrl}
                                        alt="Team Avatar"
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <div className="flex items-center justify-center w-full h-full">
                                      <IconSelector
                                          value={icon}
                                          onChange={(value) => {
                                            setIcon(value)
                                            if (errors.icon) {
                                              setErrors(prev => ({ ...prev, icon: undefined }))
                                            }
                                          }}
                                          className="flex items-center justify-center w-full h-full"
                                          iconColor={colorCodeMap[teamColor]}
                                      />
                                    </div>
                                )}
                              </div>

                              <div className="space-y-1.5">
                                <label
                                    htmlFor="avatar-upload"
                                    className="cursor-pointer inline-flex items-center gap-1.5 px-3 py-1.5 bg-muted rounded-md hover:bg-muted/80 transition-colors text-sm"
                                >
                                  <FileUp className="h-3.5 w-3.5" />
                                  <span>上传图标</span>
                                  <input
                                      id="avatar-upload"
                                      type="file"
                                      accept="image/*"
                                      className="hidden"
                                      onChange={handleAvatarChange}
                                  />
                                </label>
                                <p className="text-xs text-muted-foreground">推荐尺寸：256x256像素</p>
                              </div>
                            </div>
                            {errors.icon && (
                              <p className="text-xs text-destructive flex items-center">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                {errors.icon}
                              </p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="is-private" className="text-sm font-medium">
                              团队可见性
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <div className="flex items-center justify-between py-2">
                              <div>
                                <p className="text-sm font-medium">私有团队</p>
                                <p className="text-xs text-muted-foreground mt-0.5">启用后，只有团队成员才能查看团队信息</p>
                              </div>
                              <Switch
                                  id="is-private"
                                  checked={isPrivate}
                                  onCheckedChange={(checked) => {
                                    setIsPrivate(checked)
                                    if (errors.isPrivate) {
                                      setErrors(prev => ({ ...prev, isPrivate: undefined }))
                                    }
                                  }}
                              />
                            </div>
                            {errors.isPrivate && (
                              <p className="text-xs text-destructive flex items-center">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                {errors.isPrivate}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
            )}
          </div>
        </div>
      </div>
  )
}