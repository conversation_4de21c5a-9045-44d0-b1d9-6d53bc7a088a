"use client"

import React, { ReactNode } from "react"
import { useRouter } from "next/navigation"
import { Breadcrumb, BreadcrumbItemType, BreadcrumbExtendedProps } from "@/components/common-custom/breadcrumb"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/navigation/sidebar"

// 重新导出类型以保持向后兼容
export type BreadcrumbItem = BreadcrumbItemType

interface HeaderWithBreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
  actions?: ReactNode
  /**
   * 面包屑组件的额外属性
   */
  breadcrumbProps?: Partial<BreadcrumbExtendedProps>
}

export function HeaderWithBreadcrumb({
  items,
  className,
  actions,
  breadcrumbProps
}: HeaderWithBreadcrumbProps) {
  const router = useRouter()

  // 处理面包屑点击，使用客户端路由进行导航
  const handleBreadcrumbClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();
    router.push(href);
  };

  return (
      <header className="sticky top-0 z-10 flex h-14 shrink-0 items-center bg-background">
        <div className="flex items-center justify-between gap-2 px-6 w-full transition-all duration-200">
          <div className="flex items-center gap-2 flex-nowrap">
            <SidebarTrigger className="-ml-1" />
            <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb
              items={items}
              variant="collapsed"
              maxItems={3}
              onNavigate={handleBreadcrumbClick}
              className="whitespace-nowrap"
              {...breadcrumbProps}
            />
          </div>

          <div className="flex items-center gap-2">
            {actions}
          </div>
        </div>
      </header>
  )
}