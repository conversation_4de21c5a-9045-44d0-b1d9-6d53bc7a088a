import { useMemo } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { TruncateText } from "@/components/common-custom/truncate-text"
import { NavigationLink } from "@/components/navigation"
import {
  Users,
  Building,
  Calendar,
  CheckCircle2,
  Clock,
  TrendingUp,
  Activity,
  BarChart3,
  Layers,
  Target,
  X,
  User,
  ExternalLink,
  FileText,
  Folder,
  ListFilter,
  Settings,
  BarChart,
  AreaChart,
  Bell,
  Info,
  Flame,
  HelpCircle,
  Star,
  Plus
} from "lucide-react"

interface TeamStats {
  memberCount: number
  activeMembers: number
  subTeamCount: number
  createdAt: string
}

interface TeamOverviewPanelProps {
  stats: TeamStats
  teamName: string
  isLoading?: boolean
}

// 模拟子团队数据
const mockSubTeams = [
  { id: 1, name: "研发团队", memberCount: 8, themeColor: "blue" },
  { id: 2, name: "产品团队", memberCount: 5, themeColor: "green" },
  { id: 3, name: "设计团队", memberCount: 3, themeColor: "purple" },
  { id: 4, name: "测试团队", memberCount: 4, themeColor: "amber" }
];

// 模拟负载数据
const mockLoadData = [
  { name: "张三", load: 85 },
  { name: "李四", load: 65 },
  { name: "王五", load: 90 },
  { name: "赵六", load: 40 }
];

// 模拟团队动态数据
const mockActivities = [
  { id: 1, type: "member_added", user: "张三", target: "李四", date: "2024-04-15 14:30", userAvatar: "/placeholder.svg?height=40&width=40" },
  { id: 2, type: "project_created", user: "王五", project: "数据分析平台", date: "2024-04-12 09:15", userAvatar: "/placeholder.svg?height=40&width=40" },
  { id: 3, type: "team_updated", user: "张三", description: "更新了团队信息", date: "2024-04-10 16:45", userAvatar: "/placeholder.svg?height=40&width=40" }
];

export function TeamOverviewPanel({ stats, teamName, isLoading = false }: TeamOverviewPanelProps) {
  // 计算活跃成员比例
  const activeMemberPercentage = useMemo(() => {
    return stats.memberCount > 0 ? (stats.activeMembers / stats.memberCount * 100) : 0
  }, [stats.memberCount, stats.activeMembers])

  // 获取负载等级颜色
  const getLoadColor = (load: number) => {
    if (load >= 80) return "text-red-500 bg-red-100";
    if (load >= 60) return "text-amber-500 bg-amber-100";
    if (load >= 40) return "text-green-500 bg-green-100";
    return "text-blue-500 bg-blue-100";
  }

  // 获取活动图标
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "member_added":
        return <User className="h-4 w-4" />;
      case "project_created":
        return <FileText className="h-4 w-4" />;
      case "team_updated":
        return <Settings className="h-4 w-4" />;
      case "meeting_scheduled":
        return <Calendar className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  }

  // 获取活动描述
  const getActivityDescription = (activity: any) => {
    switch (activity.type) {
      case "member_added":
        return `添加了新成员 ${activity.target}`;
      case "project_created":
        return `创建了新项目 ${activity.project}`;
      case "team_updated":
        return activity.description;
      case "meeting_scheduled":
        return `安排了 ${activity.meeting}`;
      default:
        return "进行了操作";
    }
  }

  return (
    <>
      {isLoading ? (
        <div className="grid grid-cols-1 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="border border-border/30 shadow-sm">
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="h-4 bg-muted/60 rounded-md w-24 animate-pulse"></div>
                  <div className="h-7 bg-muted/40 rounded-md w-16 animate-pulse"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-6">
          {/* 子团队列表和负载热力图 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 子团队列表 */}
            <Card className="border border-border/30 overflow-hidden bg-card" id="subteams">
              <CardHeader className="p-5 pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">子团队列表</CardTitle>
                  <Button variant="outline" size="sm" className="h-7 px-2 text-xs cursor-pointer">
                    <Plus className="h-3.5 w-3.5 mr-1" />
                    添加子团队
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-5 pt-0">
                {mockSubTeams.length > 0 ? (
                  <div className="space-y-3">
                    {mockSubTeams.map((team) => (
                      <div 
                        key={team.id} 
                        className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border border-border/30 hover:border-border/50 transition-all"
                      >
                        <div className="flex items-center gap-3 min-w-0">
                          <div 
                            className="h-8 w-8 rounded-md flex items-center justify-center flex-shrink-0" 
                            style={{ 
                              backgroundColor: team.themeColor === "blue" ? "#dbeafe" : 
                                team.themeColor === "green" ? "#dcfce7" : 
                                team.themeColor === "purple" ? "#f3e8ff" : "#fef3c7"
                            }}
                          >
                            <Building 
                              className="h-4 w-4" 
                              style={{ 
                                color: team.themeColor === "blue" ? "#2563eb" : 
                                  team.themeColor === "green" ? "#16a34a" : 
                                  team.themeColor === "purple" ? "#9333ea" : "#d97706" 
                              }} 
                            />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium truncate">{team.name}</h4>
                              <Badge variant="outline" className="text-[10px] h-4 px-1">
                                {team.memberCount}人
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1.5">
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 cursor-pointer" asChild>
                            <NavigationLink href={`/teams/${team.id}`}>
                              <ExternalLink className="h-3 w-3" />
                            </NavigationLink>
                          </Button>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 cursor-pointer">
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <Building className="h-8 w-8 text-muted-foreground mb-2" />
                    <h4 className="text-sm font-medium">暂无子团队</h4>
                    <p className="text-xs text-muted-foreground mt-1 mb-3">
                      您可以添加子团队来组织团队结构
                    </p>
                    <Button size="sm" variant="outline" className="cursor-pointer">
                      <Plus className="h-3.5 w-3.5 mr-1.5" />
                      添加子团队
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 负载热力图和团队动态垂直排列 */}
            <div className="space-y-6">
              {/* 负载热力图 - 高度减半 */}
              <Card className="border border-border/30 overflow-hidden bg-card">
                <CardHeader className="p-4 pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-lg font-semibold">成员负载热力图</CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-5 w-5 p-0 cursor-help">
                              <HelpCircle className="h-3.5 w-3.5 text-muted-foreground" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs w-[200px]">显示团队成员当前工作负载情况，数值越高表示负载越重</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Button variant="outline" size="sm" className="h-7 px-2 text-xs cursor-pointer">
                      <ListFilter className="h-3.5 w-3.5 mr-1" />
                      筛选
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="grid grid-cols-2 gap-3">
                    {mockLoadData.map((member, index) => (
                      <div key={index} className="flex flex-col">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium truncate">{member.name}</span>
                          <Badge
                            variant="outline"
                            className={`text-xs px-1.5 ${getLoadColor(member.load)}`}
                          >
                            {member.load}%
                          </Badge>
                        </div>
                        <div className="h-1.5 bg-muted/40 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${
                              member.load >= 80 ? "bg-red-500" :
                              member.load >= 60 ? "bg-amber-500" :
                              member.load >= 40 ? "bg-green-500" :
                              "bg-blue-500"
                            }`}
                            style={{ width: `${member.load}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-end mt-2">
                    <div className="flex items-center gap-3 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        <span>低</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span>中</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                        <span>高</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        <span>超负荷</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 团队动态 - 添加回来 */}
              <Card className="border border-border/30 overflow-hidden bg-card">
                <CardHeader className="p-4 pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold">团队动态</CardTitle>
                    <Button variant="outline" size="sm" className="h-7 px-2 text-xs cursor-pointer">
                      查看全部
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="space-y-3">
                    {mockActivities.map((activity) => (
                      <div key={activity.id} className="flex gap-2">
                        <div className="flex-shrink-0">
                          <Avatar className="h-7 w-7 border border-border/30">
                            <AvatarImage src={activity.userAvatar} alt={activity.user} />
                            <AvatarFallback>{activity.user.charAt(0)}</AvatarFallback>
                          </Avatar>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <div>
                              <p className="text-sm">
                                <span className="font-medium">{activity.user}</span>{" "}
                                {getActivityDescription(activity)}
                              </p>
                              <div className="flex items-center mt-0.5 text-xs text-muted-foreground">
                                <Clock className="h-3 w-3 mr-1" />
                                <span>{activity.date}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      )}
    </>
  )
} 