"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { Bad<PERSON> } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { 
  Building, 
  ChevronRight, 
  Search, 
  Eye,
  Edit,
  MoreHorizontal,
  Calendar,
  User,
  Loader2,
  CheckCircle2,
  Plus,
  AlertTriangle
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, Toolt<PERSON>Trigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { HeaderWithBreadcrumb, type BreadcrumbItem } from "@/components/project-custom/breadcrumb"
import { TruncateText } from "@/components/common-custom/truncate-text"
import { NavigationButton, NavigationLink } from "@/components/navigation"
import { Label } from "@/components/ui/label"

// 导入API方法和类型
import { queryTeams, deleteTeamRequest, updateTeamRequest, TeamDetail } from "@/services/api/teamRequestApi"

// 定义前端团队数据类型
interface TeamData {
  id: string;
  name: string;
  code: string;
  description: string;
  memberCount: number;
  status: string;
  statusColor: string;
  lastUpdated: string;
  createTime: string;
    parentCode?: string;
  parentName?: string;
  type: string;
  leader?: string;
}

// 转换团队数据为前端展示格式
const convertTeamData = (team: any): TeamData => {
  // 防御性编程，确保所有字段都有合理的默认值
  if (!team) {
    return {
      id: '',
      code: '',
      name: '',
      description: '',
      memberCount: 0,
      status: "未知",
      statusColor: "gray", 
      lastUpdated: '',
      createTime: '',
      leader: "未知",
      type: "tech",
      parentCode: undefined,
      parentName: '',
    };
  }
  
  return {
    id: team.id?.toString() || '',
    code: team.teamCode || '',                      // 团队编号
    name: team.teamName || '',                      // 团队名称
    description: team.description || '',            // 团队描述
    memberCount: typeof team.memberCount === 'number' ? team.memberCount : 0,
    status: team.statusLabel || (team.status === 1 ? "正常" : "禁用"),  // 团队状态中文
    statusColor: team.teamThemeColor ? team.teamThemeColor.replace('#', '') : "gray", 
    lastUpdated: team.updateTime?.substring(0, 16) || '',  // 更新时间
    createTime: team.createTime?.substring(0, 16) || '',   // 创建时间
    leader: team.leaderId || "管理员",
    type: team.teamType || "tech",
      parentCode: team.parentCode ? team.parentCode.toString() : undefined,
    parentName: team.parentTeamName || '',          // 父级团队名称
  }
}

export default function TeamsPage() {
    // 状态定义
    const [searchQuery, setSearchQuery] = useState("")
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
    const [teamToDelete, setTeamToDelete] = useState<TeamData | null>(null)
    const [deleteConfirmName, setDeleteConfirmName] = useState("")
    const [isDeleting, setIsDeleting] = useState(false)
    
    // 团队数据状态
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [teamsList, setTeamsList] = useState<TeamData[]>([])
    const [lastRequestKey, setLastRequestKey] = useState<string>("")
    const [selectedTeamId, setSelectedTeamId] = useState<string | undefined>(undefined)

    // 面包屑项目
    const breadcrumbItems: BreadcrumbItem[] = [
      { label: "首页", href: "/" },
      { label: "团队管理", isCurrent: true }
    ]

    // 加载团队数据
    const loadTeams = useCallback(async () => {
      // 构建请求缓存键，仅保留搜索条件
      const requestKey = `${searchQuery}`;
      
      // 如果请求参数没变且非首次加载，则不重复请求
      if (requestKey === lastRequestKey && !loading) {
        return;
      }
      
      setLoading(true);
      setError(null);
      
      try {
        // 使用新的查询方法，只传递搜索关键字
        const result = await queryTeams(searchQuery);

        if (result && Array.isArray(result.data)) {
          // 确保返回的数据是数组，并且每个元素都转换为标准格式
          const validTeams = (result.data || []).filter((item: any) => item !== null && typeof item === 'object');
          setTeamsList(validTeams.map((item: any) => convertTeamData(item)));
        } else {
          console.error('返回的数据结构不符合预期');
          setTeamsList([]);
        }
        
        // 更新请求缓存键
        setLastRequestKey(requestKey);
      } catch (err) {
        console.error("加载团队数据失败", err);
        setError("加载团队数据失败，请稍后重试");
      } finally {
        setLoading(false);
      }
    }, [searchQuery, lastRequestKey, loading]);
    
    // 优化依赖项，避免重复渲染
    const depsArray = useMemo(() => {
      return [searchQuery];
    }, [searchQuery]);
    
    // 初始加载和查询条件变化时重新加载数据
    useEffect(() => {
      const timer = setTimeout(() => {
        loadTeams()
      }, 300)
      
      return () => clearTimeout(timer)
    }, depsArray); // 使用优化后的依赖数组

    // 处理删除团队
    const handleDeleteTeam = async () => {
      if (!teamToDelete) return
      
      try {
        setIsDeleting(true)
        const success = await deleteTeamRequest(teamToDelete.id)
        if (success) {
          loadTeams()
        }
      } catch (err) {
        console.error("删除团队失败", err)
        setError("删除团队失败，请稍后重试")
      } finally {
        setTeamToDelete(null)
        setDeleteConfirmName("")
        setIsDeleteDialogOpen(false)
        setIsDeleting(false)
      }
    }
    
    // 处理更新团队状态（启用/禁用）
    const handleToggleTeamStatus = async (team: TeamData) => {
      try {
        const newStatus = team.status === "启用" ? "禁用" : "启用"
        // 根据实际API参数结构调整
        const updateData = {
          id: team.id,
          status: newStatus === "启用" ? 1 : 0
        }
        await updateTeamRequest(updateData)
        loadTeams()
      } catch (err) {
        console.error("更新团队状态失败", err)
        setError(`${team.status === "启用" ? "禁用" : "启用"}团队失败，请稍后重试`)
      }
    }
    
    // 打开删除确认对话框
    const openDeleteDialog = (team: TeamData) => {
      setTeamToDelete(team)
      setDeleteConfirmName("")
      setIsDeleteDialogOpen(true)
    }
    
    // 检查删除确认名称是否正确
    const isDeleteConfirmValid = () => {
      return teamToDelete && deleteConfirmName === teamToDelete.name
    }

    // 计算过滤后的数据
    const filteredTeams = useMemo(() => {
      // 应用搜索过滤
      return teamsList.filter(team => {
        // 搜索过滤
        const matchesSearch = searchQuery.trim() === "" || 
                           (team.name?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
                           (team.description?.toLowerCase() || '').includes(searchQuery.toLowerCase()) || 
                           (team.code?.toLowerCase() || '').includes(searchQuery.toLowerCase());
        
        return matchesSearch;
      });
    }, [teamsList, searchQuery]);

    // 获取团队主题色
    const getTeamThemeColor = (color: string) => {
      if (!color || color === "gray") {
        return "#6B7280"; // 灰色
      }
      
      // 处理不同格式的颜色
      if (color.startsWith("#")) {
        return color;
      }
      
      // 映射颜色名称到十六进制
      const colorMap: Record<string, string> = {
        "red": "#EF4444",
        "green": "#10B981",
        "blue": "#3B82F6",
        "yellow": "#F59E0B",
        "purple": "#8B5CF6",
        "pink": "#EC4899",
        "indigo": "#6366F1",
        "gray": "#6B7280",
      };
      
      return colorMap[color] || "#6B7280";
    };

    // 表格视图组件
    const renderTableView = () => {
        return (
            <div className="border border-border/30 rounded-lg overflow-hidden shadow-sm">
                <Table>
                    <TableHeader className="bg-muted/30">
                        <TableRow className="hover:bg-transparent">
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead className="w-[100px]">Logo</TableHead>
                            <TableHead className="w-[120px]">编号</TableHead>
                            <TableHead className="w-[180px]">
                                <div className="flex gap-2 items-center">
                                    团队名称
                                </div>
                            </TableHead>
                            <TableHead className="w-[80px]">状态</TableHead>
                            <TableHead className="w-[80px]">类型</TableHead>
                            <TableHead className="w-[150px]">父级团队</TableHead>
                            <TableHead>
                                <div className="flex gap-2 items-center">
                                    团队描述
                                </div>
                            </TableHead>
                            <TableHead className="w-[160px]">
                                <div className="flex gap-2 items-center">
                                    创建时间
                                </div>
                            </TableHead>
                            <TableHead className="w-[120px] text-left">操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredTeams.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={9} className="h-24 text-center">
                                    {loading ? (
                                        <div className="flex items-center justify-center">
                                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                            加载中...
                                        </div>
                                    ) : (
                                        "没有符合条件的团队"
                                    )}
                                </TableCell>
                            </TableRow>
                        ) : (
                            filteredTeams.map((team, index) => (
                            <TableRow 
                              key={team.id} 
                                className={`cursor-pointer hover:bg-muted/30 transition-colors ${selectedTeamId === team.id ? 'bg-muted/50' : ''}`}
                              onClick={() => setSelectedTeamId(team.id)}
                            >
                                <TableCell className="font-medium">{index + 1}</TableCell>
                                <TableCell>
                                    <div className="h-8 w-8 rounded-md flex items-center justify-center bg-muted/30">
                                        <Building className="h-5 w-5" style={{ color: getTeamThemeColor(team.statusColor) }} />
                                    </div>
                                </TableCell>
                                <TableCell className="font-medium">{team.code || "--"}</TableCell>
                                <TableCell>
                                    <div className="flex items-center gap-2">
                                        <div 
                                          className="size-6 flex-shrink-0 rounded-md flex items-center justify-center"
                                          style={{ 
                                            backgroundColor: `${getTeamThemeColor(team.statusColor)}20`,
                                          }}
                                        >
                                            <Building className="h-4 w-4" style={{ color: getTeamThemeColor(team.statusColor) }} />
                                        </div>
                                        <TruncateText text={team.name} maxWidth="180px" />
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <Badge 
                                      variant={team.status === "启用" ? "default" : "destructive"} 
                                      className={`${team.status === "启用" ? "bg-green-500/80" : ""}`}
                                    >
                                      {team.status}
                                    </Badge>
                                </TableCell>
                                <TableCell>
                                    {team.parentName ? (
                                        <Badge variant="outline" className="text-[10px] h-4 px-1">
                                            子团队
                                        </Badge>
                                    ) : (
                                        <Badge variant="outline" className="text-[10px] h-4 px-1 bg-primary/5">
                                            顶级团队
                                        </Badge>
                                    )}
                                </TableCell>
                                <TableCell>
                                    {team.parentName ? (
                                        <TruncateText
                                          text={team.parentName}
                                          className="max-w-[100px] inline-block text-sm"
                                        />
                                    ) : (
                                        <span className="text-muted-foreground text-sm">--</span>
                                    )}
                                </TableCell>
                                <TableCell>
                                    {team.description ? (
                                        <TruncateText
                                          text={team.description}
                                          className="max-w-[200px] inline-block text-sm"
                                        />
                                    ) : (
                                        <span className="text-muted-foreground text-sm">--</span>
                                    )}
                                </TableCell>
                                <TableCell>
                                    <span>{team.createTime || "--"}</span>
                                </TableCell>
                                <TableCell className="text-left">
                                    <div className="flex gap-1">
                                        <Button variant="ghost" size="sm" asChild className="h-7 px-2 cursor-pointer">
                                            <NavigationLink href={`/teams/${team.id}`}>
                                                <Eye className="h-3.5 w-3.5" />
                                            </NavigationLink>
                                        </Button>
                                        <Button variant="ghost" size="sm" asChild className="h-7 px-2 cursor-pointer">
                                            <NavigationLink href={`/teams/${team.id}?mode=edit`}>
                                                <Edit className="h-3.5 w-3.5" />
                                            </NavigationLink>
                                        </Button>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm" className="h-7 px-2 cursor-pointer">
                                                    <MoreHorizontal className="h-3.5 w-3.5" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end" className="w-[120px] border border-border/30 shadow-md">
                                                <DropdownMenuItem 
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleToggleTeamStatus(team);
                                                    }}
                                                    className="text-left cursor-pointer"
                                                >
                                                    {team.status === "启用" ? "禁用团队" : "启用团队"}
                                                </DropdownMenuItem>
                                                <DropdownMenuItem 
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        openDeleteDialog(team);
                                                    }}
                                                    className="text-destructive focus:text-destructive text-left cursor-pointer"
                                                >
                                                    删除团队
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </TableCell>
                            </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>
            </div>
        )
    }
    
    // 创建团队按钮
    const actionButtons = (
        <div className="flex items-center gap-2">
            <NavigationButton 
                href="/teams/new" 
                variant="default" 
                className="shadow-sm cursor-pointer"
            >
                <Plus className="h-4 w-4 mr-1" />
                创建团队
            </NavigationButton>
        </div>
    );

    // 筛选和查询控件
    const filterControls = (
        <div className="w-full space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
                {/* 搜索框 */}
                <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        type="search"
                        placeholder="搜索团队..."
                        className="pl-8 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
            </div>
        </div>
    );

    // 页面内容
    return (
        <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-background to-background/80">
            <HeaderWithBreadcrumb items={breadcrumbItems} />
            
            <div className="p-4 md:p-6 max-w-7xl mx-auto w-full">
                <main className="space-y-6">
                    {/* 优化顶部布局 */}
                    <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div className="flex items-center gap-2">
                            <h1 className="text-2xl font-bold tracking-tight">团队管理</h1>
                            <Badge variant="outline" className="ml-2">{filteredTeams.length}</Badge>
                        </div>
                        {actionButtons}
                    </div>

                    {/* 视图切换和搜索工具栏 - 优化布局 */}
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                        {filterControls}
                    </div>

                    {/* 显示错误信息 */}
                    {error && (
                        <Alert variant="destructive">
                            <AlertTitle>加载错误</AlertTitle>
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    {/* 内容区优化 */}
                    <div className="relative min-h-[300px] mt-2">
                        {/* 团队数据 */}
                        {renderTableView()}
                    </div>
                </main>
            </div>
            
            {/* 删除确认对话框 */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="max-w-[450px] border border-destructive/20">
                    <DialogHeader>
                        <DialogTitle className="text-destructive">确认删除团队</DialogTitle>
                        <DialogDescription>
                            此操作将永久删除该团队及其所有数据，且<span className="font-semibold">不可恢复</span>。请输入团队名称 <span className="font-medium">{teamToDelete?.name}</span> 以确认删除。
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-6">
                        <div className="space-y-4">
                            <div className="bg-destructive/5 p-3 rounded-md border border-destructive/20 text-sm">
                                <div className="flex gap-2 items-start">
                                    <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
                                    <div>
                                        <p className="font-medium text-foreground mb-1">删除后，以下内容将被永久删除：</p>
                                        <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                                            <li>团队所有成员关联关系</li>
                                            <li>团队角色和权限设置</li>
                                            <li>团队基本信息和配置</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="confirm-delete" className="text-sm font-medium">
                                    输入团队名称确认删除
                                </Label>
                                <Input
                                    id="confirm-delete"
                                    placeholder={`请输入：${teamToDelete?.name}`}
                                    value={deleteConfirmName}
                                    onChange={(e) => setDeleteConfirmName(e.target.value)}
                                    className="border-destructive/50 focus-visible:ring-destructive bg-destructive/5"
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setIsDeleteDialogOpen(false)}
                            disabled={isDeleting}
                        >
                            取消
                        </Button>
                        <Button 
                            variant="destructive" 
                            onClick={handleDeleteTeam}
                            disabled={!isDeleteConfirmValid() || isDeleting}
                        >
                            {isDeleting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    删除中...
                                </>
                            ) : (
                                '删除团队'
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}

