"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { Pagination } from "@/components/common-custom/pagination"
import { DataTableToolbar } from "./toolbar"
import { DataTableColumnHeader } from "./column-header"
import {
  type AdvancedDataTableProps,
  type TableConfig,
  type TableAction,
  type TableFilter,
  DEFAULT_TABLE_CONFIG
} from "./types"

// 使用导入的默认配置
const defaultConfig: TableConfig = DEFAULT_TABLE_CONFIG

export function AdvancedDataTable<TData, TValue>({
  columns,
  data,
  config = {},
  searchKey,
  filters = [],
  primaryActions = [],
  secondaryActions = [],
  onRowAction,
  loading = false,
  className,
  onRefresh,
}: AdvancedDataTableProps<TData, TValue>) {
  const tableConfig = { ...defaultConfig, ...config }
  const [density, setDensity] = React.useState<"compact" | "default" | "comfortable">("default")
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [searchValue, setSearchValue] = React.useState("")

  // 当searchValue变化时，更新列过滤器
  React.useEffect(() => {
    if (searchKey) {
      setColumnFilters(prev => {
        const newFilters = prev.filter(filter => filter.id !== searchKey);
        if (searchValue) {
          newFilters.push({
            id: searchKey,
            value: searchValue,
          });
        }
        return newFilters;
      });
    }
  }, [searchValue, searchKey]);

  // 构建列定义
  const columnsWithExtras = React.useMemo(() => {
    let newColumns = [...columns]

    // 添加选择列
    if (tableConfig.showSelection) {
      const selectionColumn: ColumnDef<TData, TValue> = {
        id: "select",
        header: ({ table }) => (
          <div className="pl-4">
            <Checkbox
              checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
              aria-label="全选"
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="pl-4">
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              aria-label="选择行"
            />
          </div>
        ),
        enableSorting: false,
        enableHiding: false,
      }
      newColumns = [selectionColumn, ...newColumns]
    }

    // 为其他所有列添加列标题组件
    newColumns = newColumns.map(column => {
      if (column.id !== "select" && column.id !== "actions") {
        return {
          ...column,
          header: typeof column.header === 'function' 
            ? column.header 
            : (props: any) => <DataTableColumnHeader column={props.column} title={column.header as string} />,
        }
      }
      return column;
    });

    // 添加操作列
    if (tableConfig.showRowActions && (primaryActions.length > 0 || secondaryActions.length > 0)) {
      const actionsColumn: ColumnDef<TData, TValue> = {
        id: "actions",
        header: () => <div className="text-left pr-4">操作</div>,
        cell: ({ row }) => {
          // 合并所有操作按钮
          const allActions = [...primaryActions, ...secondaryActions];
          // 如果总按钮数小于等于2个，则全部直接显示
          const visibleActions = allActions.length <= 2 ? allActions : allActions.slice(0, 2);
          // 如果总按钮数大于2个，则剩余的放入更多菜单
          const moreActions = allActions.length <= 2 ? [] : allActions.slice(2);

          return (
            <div className="flex items-center justify-end gap-2 pr-4">
              {visibleActions.map((action) => (
                <Button
                  key={action.value}
                  variant="ghost"
                  size="icon"
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => {
                    action.onClick?.(row.original)
                    onRowAction?.(row.original, action.value)
                  }}
                  title={action.label}
                >
                  {action.icon && <action.icon className="h-4 w-4" />}
                </Button>
              ))}

              {moreActions.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="cursor-pointer hover:bg-muted">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">更多操作</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="min-w-[120px] p-1">
                    {moreActions.map((action) => (
                      <DropdownMenuItem
                        key={action.value}
                        onClick={() => {
                          action.onClick?.(row.original)
                          onRowAction?.(row.original, action.value)
                        }}
                        className="text-foreground text-sm py-1.5 cursor-pointer hover:bg-muted"
                      >
                        {action.icon && <action.icon className="mr-2 h-3.5 w-3.5" />}
                        {action.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          )
        },
                  enableSorting: false,
          enableHiding: false,
          size: (primaryActions.length + secondaryActions.length) > 0 
            ? ((primaryActions.length + secondaryActions.length) > 1 ? 100 : 60) 
            : 60,
      }
      newColumns = [...newColumns, actionsColumn]
    }

    return newColumns
  }, [columns, tableConfig.showSelection, tableConfig.showRowActions, primaryActions, secondaryActions, onRowAction])

  const table = useReactTable({
    data,
    columns: columnsWithExtras,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    initialState: {
      pagination: {
        pageSize: tableConfig.pageSize || 10,
      },
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  // 渲染筛选器
  const renderFilter = (filter: TableFilter) => {
    const column = table.getColumn(filter.key)
    if (!column) return null

    return filter.type === "search" ? (
      <div key={filter.key} className="relative">
        <Input
          placeholder={filter.placeholder || `搜索${filter.title}...`}
          value={(column.getFilterValue() as string) ?? ""}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => column.setFilterValue(event.target.value)}
          className="pl-8 w-[200px]"
        />
      </div>
    ) : null
  }

  // 获取表格行高度
  const getTableRowHeight = () => {
    switch (density) {
      case "compact":
        return "h-8";
      case "comfortable":
        return "h-12";
      default:
        return "h-10";
    }
  };

  // 获取表格单元格内边距
  const getTableCellPadding = () => {
    switch (density) {
      case "compact":
        return "py-1";
      case "comfortable":
        return "py-3";
      default:
        return "py-2";
    }
  };

  return (
    <div className={cn("w-full relative", className)}>
      {/* 工具栏 */}
      {(tableConfig.showSearch || tableConfig.showFilters || tableConfig.showColumnVisibility) && (
        <DataTableToolbar
          searchKey={searchKey || ""}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
          filters={filters}
          renderFilter={renderFilter}
          columns={columns}
          columnVisibility={columnVisibility}
          onColumnVisibilityChange={setColumnVisibility}
          density={density}
          onDensityChange={setDensity}
          onRefresh={onRefresh}
        />
      )}

      {/* 表格 */}
      <div className="rounded-md border overflow-hidden">
        <div className="relative w-full overflow-auto">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-muted/50">
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead 
                        key={header.id}
                        style={{ width: header.getSize() }}
                        className={getTableCellPadding()}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                Array.from({ length: tableConfig.pageSize || 10 }).map((_, index) => (
                  <TableRow key={index} className={getTableRowHeight()}>
                    {columnsWithExtras.map((_, cellIndex) => (
                      <TableCell key={cellIndex} className={getTableCellPadding()}>
                        <Skeleton className="h-4 w-full" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow 
                    key={row.id} 
                    data-state={row.getIsSelected() && "selected"}
                    className={getTableRowHeight()}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={getTableCellPadding()}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columnsWithExtras.length} className="h-24 text-center">
                    {tableConfig.emptyMessage}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* 分页 */}
      {tableConfig.showPagination && (
        <Pagination
          pageIndex={table.getState().pagination.pageIndex}
          pageCount={table.getPageCount()}
          pageSize={table.getState().pagination.pageSize}
          pageSizeOptions={tableConfig.pageSizeOptions}
          totalItems={table.getFilteredRowModel().rows.length}
          onPageChange={(page) => table.setPageIndex(page)}
          onPageSizeChange={(size) => table.setPageSize(size)}
        />
      )}
    </div>
  )
}

// ============================================================================
// 统一导出 - 复杂组件按分类导出
// ============================================================================

// 子组件导出
export { DataTableToolbar } from "./toolbar"
export { DataTableColumnHeader } from "./column-header"

// 类型导出
export * from "./types"

// JSON表格相关导出
export * from "./json-table"

// 默认配置导出
export { DEFAULT_TABLE_CONFIG, DEFAULT_TOOLBAR_CONFIG, DEFAULT_ACTION_CONFIG } from "./types"