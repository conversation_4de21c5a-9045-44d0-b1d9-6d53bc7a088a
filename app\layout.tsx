import {ReactNode} from "react";
import { <PERSON><PERSON>, Noto_Sans_SC } from "next/font/google";
import "./globals.css";
import { GlobalNavigation } from "@/components/navigation/global-navigation"
import { SidebarInset } from "@/components/navigation/sidebar"
import { cn } from "@/lib/utils";
import { Providers } from "@/components/providers";
import { PageLoading } from "@/components/common-custom/page-loading";
import { StagewiseToolbarWrapper } from "@/components/project-custom/stagewise-toolbar";

// 配置Roboto字体 - 用于英文和数字
const roboto = Roboto({
  weight: ['400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

// 配置Noto Sans SC字体 - 用于中文
const notoSansSC = Noto_Sans_SC({
  weight: ['400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-noto-sans-sc',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={cn(
        "min-h-screen bg-background antialiased font-sans",
        roboto.variable,
        notoSansSC.variable
      )}>
        <Providers>
          <div className="flex min-h-screen w-full">
            <GlobalNavigation />
            <SidebarInset>
              {children}
            </SidebarInset>
            <PageLoading />
          </div>
          <StagewiseToolbarWrapper />
        </Providers>
      </body>
    </html>
  );
}

