"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { But<PERSON> } from "@/components/ui/button"
import {
  showToast,
  NotificationCenter,
  AnnouncementBanner,
  NotificationSettings
} from "@/components/project-custom/notification"
import { Bell, Info, AlertTriangle, CheckCircle, XCircle, Mail, Smartphone, Settings } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// API文档组件
function NotificationApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">通知组件API</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">组件</th>
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border" rowSpan={3}>showToast</td>
                <td className="p-2 border">type</td>
                <td className="p-2 border">success | error | warning | info</td>
                <td className="p-2 border">info</td>
                <td className="p-2 border">通知类型</td>
              </tr>
              <tr>
                <td className="p-2 border">message</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">通知消息内容</td>
              </tr>
              <tr>
                <td className="p-2 border">duration</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">3000</td>
                <td className="p-2 border">通知显示时间(毫秒)</td>
              </tr>
              <tr>
                <td className="p-2 border" rowSpan={8}>NotificationCenter</td>
                <td className="p-2 border">notifications</td>
                <td className="p-2 border">NotificationItem[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">通知数据数组</td>
              </tr>
              <tr>
                <td className="p-2 border">onNotificationClick</td>
                <td className="p-2 border">function</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">点击通知的回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onMarkAsRead</td>
                <td className="p-2 border">function</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标记已读的回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onMarkAllAsRead</td>
                <td className="p-2 border">function</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标记全部已读的回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onRemove</td>
                <td className="p-2 border">function</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">移除通知的回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onClearAll</td>
                <td className="p-2 border">function</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">清空所有通知的回调</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">通知中心</td>
                <td className="p-2 border">标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">您的最新通知和提醒</td>
                <td className="p-2 border">描述文本</td>
              </tr>
              <tr>
                <td className="p-2 border" rowSpan={7}>AnnouncementBanner</td>
                <td className="p-2 border">type</td>
                <td className="p-2 border">info | warning | success | error</td>
                <td className="p-2 border">info</td>
                <td className="p-2 border">横幅类型</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">横幅标题</td>
              </tr>
              <tr>
                <td className="p-2 border">content</td>
                <td className="p-2 border">string | ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">横幅内容</td>
              </tr>
              <tr>
                <td className="p-2 border">date</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">日期时间</td>
              </tr>
              <tr>
                <td className="p-2 border">important</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否重要</td>
              </tr>
              <tr>
                <td className="p-2 border">onClose</td>
                <td className="p-2 border">function</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">关闭回调</td>
              </tr>
              <tr>
                <td className="p-2 border">closable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否可关闭</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function NotificationPreview() {
  const examples = [
    {
      id: "basic-notification",
      title: "基础通知",
      description: "基础的弹出式通知，支持成功、错误、警告和信息四种类型",
      code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { showToast } from "@/components/project-custom/notification";
import { useToast } from "@/hooks/use-toast";

function BasicNotification() {
  const { toast } = useToast();
  
  const handleShowToast = (type) => {
    showToast(
      {
      type,
      message: \`这是一条\${
        type === "success" ? "成功" : 
        type === "error" ? "错误" : 
        type === "warning" ? "警告" : "信息"
      }通知\`
      },
      {
        show: ({ title, description, variant, duration }) => {
          toast({
            title,
            description,
            variant,
            duration,
          });
        }
      }
    );
  };

  return (
    <div className="flex flex-wrap gap-4 justify-center">
      <Button 
        onClick={() => handleShowToast("success")}
        variant="default"
      >
        成功通知
      </Button>
      <Button 
        onClick={() => handleShowToast("error")}
        variant="destructive"
      >
        错误通知
      </Button>
      <Button 
        onClick={() => handleShowToast("warning")}
        variant="outline"
      >
        警告通知
      </Button>
      <Button 
        onClick={() => handleShowToast("info")}
        variant="secondary"
      >
        信息通知
      </Button>
    </div>
  );
}

render(<BasicNotification />);`,
      scope: { 
        React, 
        Button, 
        showToast,
        useToast
      },
    },
    {
      id: "announcement-banner",
      title: "公告横幅",
      description: "用于展示系统公告、重要提醒等信息的横幅组件",
      code: `
import React from "react";
import { AnnouncementBanner } from "@/components/common-custom/notification";

function AnnouncementBannerExample() {
  return (
    <div className="flex flex-col gap-4">
      <AnnouncementBanner
        id="system-update"
        type="info"
        title="系统更新通知"
        content="系统将于今晚22:00-24:00进行升级维护，请提前做好准备工作。"
        date="2024-05-10 10:30"
        onClose={() => {}}
      />
      <AnnouncementBanner
        id="security-alert"
        type="warning"
        title="安全警告"
        content="检测到异常登录行为，请及时修改密码并开启双因素认证。"
        date="2024-05-09 15:45"
        onClose={() => {}}
      />
      <AnnouncementBanner
        id="success-operation"
        type="success"
        title="操作成功"
        content="您的订单已经成功提交，我们将尽快处理。"
        date="2024-05-08 09:15"
        onClose={() => {}}
      />
    </div>
  );
}

render(<AnnouncementBannerExample />);`,
      scope: { 
        React, 
        AnnouncementBanner 
      },
    },
    {
      id: "notification-center",
      title: "通知中心",
      description: "集中管理和展示多种类型通知的中心面板",
      code: `
import React, { useState } from "react";
import { NotificationCenter } from "@/components/common-custom/notification";
import { Button } from "@/components/ui/button";

function NotificationCenterExample() {
  const [notifications, setNotifications] = useState([
    {
      id: "1",
      title: "系统更新完成",
      message: "系统已更新到最新版本，新增多项功能优化。",
      date: "10分钟前",
      type: "info",
      read: false
    },
    {
      id: "2",
      title: "安全警告",
      message: "检测到异常登录行为，请及时修改密码。",
      date: "30分钟前",
      type: "warning",
      read: false
    },
    {
      id: "3",
      title: "订单已发货",
      message: "您的订单 #12345 已发货，预计3天内送达。",
      date: "2小时前",
      type: "success",
      read: true,
      actions: [
        {
          label: "查看订单",
          onClick: () => alert("查看订单详情"),
          primary: true
        }
      ]
    }
  ]);

  const handleMarkAsRead = (id) => {
    setNotifications(
      notifications.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(
      notifications.map(notification => ({ ...notification, read: true }))
    );
  };

  const handleClearAll = () => {
    setNotifications([]);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="max-w-md mx-auto">
        <NotificationCenter
          notifications={notifications}
          onNotificationClick={(notification) => alert(\`点击了: \${notification.title}\`)}
          onMarkAsRead={handleMarkAsRead}
          onMarkAllAsRead={handleMarkAllAsRead}
          onClearAll={handleClearAll}
        />
      </div>
    </div>
  );
}

render(<NotificationCenterExample />);`,
      scope: { 
        React, 
        NotificationCenter, 
        Button, 
        useState: React.useState 
      },
    },
    {
      id: "notification-settings",
      title: "通知设置",
      description: "用户通知偏好设置面板，管理接收通知的方式",
      code: `
import React, { useState } from "react";
import { NotificationSettings } from "@/components/common-custom/notification";
import { Mail, Bell, Smartphone, Settings } from "lucide-react";

function NotificationSettingsExample() {
  const [settings, setSettings] = useState([
    {
      id: "email",
      label: "电子邮件通知",
      description: "接收重要信息和更新",
      enabled: true,
      icon: Mail
    },
    {
      id: "push",
      label: "推送通知",
      description: "接收实时应用推送",
      enabled: true,
      icon: Bell
    },
    {
      id: "sms",
      label: "短信通知",
      description: "接收重要提醒和安全验证",
      enabled: false,
      icon: Smartphone
    },
    {
      id: "system",
      label: "系统通知",
      description: "接收系统更新和维护信息",
      enabled: true,
      icon: Settings
    }
  ]);

  const handleSave = (updatedSettings) => {
    setSettings(updatedSettings);
    alert("设置已保存");
  };

  return (
    <div className="max-w-md mx-auto">
      <NotificationSettings
        settings={settings}
        onSave={handleSave}
        onReset={() => setSettings([...settings])}
      />
    </div>
  );
}

render(<NotificationSettingsExample />);`,
      scope: { 
        React, 
        NotificationSettings, 
        useState: React.useState,
        Mail,
        Bell,
        Smartphone,
        Settings
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="通知 Notification"
      description="用于向用户展示提示信息、警告、错误或成功消息的组件"
      whenToUse="当需要向用户提供反馈、提示重要信息、展示系统状态或需要用户注意时使用"
      examples={examples}
      apiDocs={<NotificationApiDocs />}
    />
  );
}
 