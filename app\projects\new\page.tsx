"use client"

import { useState } from "react"
import { use<PERSON>outer } from "next/navigation"
import { 
  Calendar,
  Check, 
  ChevronsUpDown, 
  Info, 
  Tag, 
  Users,
  ArrowLeft, 
  Clock, 
  Plus,
  Trash
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { DatePicker } from "@/components/ui/date-picker"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { HeaderWithBreadcrumb, type BreadcrumbItem } from "@/components/project-custom/breadcrumb"
import { BackButton } from "@/components/common-custom/back-button"
import { cn } from "@/lib/utils"

// 模拟团队数据
const teamOptions = [
  { label: "前端团队", value: "1" },
  { label: "后端团队", value: "2" },
  { label: "设计团队", value: "3" },
  { label: "产品团队", value: "4" },
  { label: "测试团队", value: "5" },
]

// 常用标签
const commonTags = [
  "前端", "后端", "设计", "产品", "测试", "文档",
  "重构", "优化", "移动端", "Web", "API", "数据库",
  "安全", "性能", "UI/UX", "微服务"
]

export default function NewProjectPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    teamId: "",
    teamName: "",
    startDate: null as Date | null,
    dueDate: null as Date | null,
    priority: "中",
    status: "规划中",
    tags: [] as string[],
    isPrivate: false,
    customTag: ""
  })
  
  // 面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "首页", href: "/" },
    { label: "项目管理", href: "/projects" },
    { label: "创建项目", isCurrent: true }
  ]
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      alert("请输入项目名称")
      return
    }
    
    setIsSubmitting(true)
    
    // 模拟API请求
    setTimeout(() => {
      console.log("提交的项目数据:", formData)
      setIsSubmitting(false)
      router.push("/projects")
    }, 1500)
  }
  
  // 添加标签
  const addTag = (tag: string) => {
    if (!tag.trim() || formData.tags.includes(tag.trim())) return
    
    setFormData({
      ...formData,
      tags: [...formData.tags, tag.trim()],
      customTag: ""
    })
  }
  
  // 删除标签
  const removeTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    })
  }
  
  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-background to-background/80">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      
      <div className="p-4 md:p-6 max-w-4xl mx-auto w-full">
        <main className="space-y-6">
          {/* 标题和返回按钮 */}
          <div className="flex items-center gap-3">
            <BackButton href="/projects" />
            <div>
              <h1 className="text-2xl font-bold tracking-tight">创建新项目</h1>
              <p className="text-muted-foreground mt-1">创建一个新的项目并设置基本信息</p>
            </div>
          </div>
          
          {/* 创建表单 */}
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* 基本信息卡片 */}
              <Card className="border border-border/30">
                <CardHeader>
                  <CardTitle className="text-xl">基本信息</CardTitle>
                  <CardDescription>设置项目的基本信息</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="project-name">
                      项目名称 <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id="project-name"
                      placeholder="输入项目名称"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="border border-input/30 bg-background/50"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="project-description">项目描述</Label>
                    <Textarea
                      id="project-description"
                      placeholder="描述项目的目标、范围和关键特性"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="min-h-[120px] border border-input/30 bg-background/50 resize-none"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="team">所属团队</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="w-full justify-between border border-input/30 bg-background/50"
                          >
                            {formData.teamName || "选择团队"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput placeholder="搜索团队..." />
                            <CommandList>
                              <CommandEmpty>未找到相关团队</CommandEmpty>
                              <CommandGroup>
                                {teamOptions.map((team) => (
                                  <CommandItem
                                    key={team.value}
                                    value={team.label}
                                    onSelect={() => {
                                      setFormData({
                                        ...formData,
                                        teamId: team.value,
                                        teamName: team.label
                                      })
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        formData.teamId === team.value
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {team.label}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>项目状态</Label>
                      <RadioGroup
                        defaultValue="规划中"
                        value={formData.status}
                        onValueChange={(value) => setFormData({ ...formData, status: value })}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="规划中" id="status-planning" />
                          <Label htmlFor="status-planning" className="cursor-pointer">规划中</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="进行中" id="status-progress" />
                          <Label htmlFor="status-progress" className="cursor-pointer">进行中</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* 日期与优先级卡片 */}
              <Card className="border border-border/30">
                <CardHeader>
                  <CardTitle className="text-xl">日期与优先级</CardTitle>
                  <CardDescription>设置项目的时间范围和优先级</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>开始日期</Label>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <DatePicker
                          value={formData.startDate}
                          onChange={(date) => setFormData({ ...formData, startDate: date })}
                          placeholder="选择开始日期"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>截止日期</Label>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <DatePicker
                          value={formData.dueDate}
                          onChange={(date) => setFormData({ ...formData, dueDate: date })}
                          placeholder="选择截止日期"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>优先级</Label>
                    <RadioGroup
                      defaultValue="中"
                      value={formData.priority}
                      onValueChange={(value) => setFormData({ ...formData, priority: value })}
                      className="flex gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="高" id="priority-high" />
                        <Label htmlFor="priority-high" className="cursor-pointer flex items-center">
                          <Badge variant="outline" className="ml-1 text-red-600 bg-red-100">高</Badge>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="中" id="priority-medium" />
                        <Label htmlFor="priority-medium" className="cursor-pointer flex items-center">
                          <Badge variant="outline" className="ml-1 text-amber-600 bg-amber-100">中</Badge>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="低" id="priority-low" />
                        <Label htmlFor="priority-low" className="cursor-pointer flex items-center">
                          <Badge variant="outline" className="ml-1 text-blue-600 bg-blue-100">低</Badge>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                </CardContent>
              </Card>
              
              {/* 标签与可见性卡片 */}
              <Card className="border border-border/30">
                <CardHeader>
                  <CardTitle className="text-xl">标签与可见性</CardTitle>
                  <CardDescription>添加项目标签和设置可见性</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>项目标签</Label>
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      {formData.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="bg-muted/40 flex items-center gap-1"
                        >
                          {tag}
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeTag(tag)}
                            className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground hover:bg-transparent cursor-pointer"
                          >
                            <Trash className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                      {formData.tags.length === 0 && (
                        <span className="text-sm text-muted-foreground">暂无标签</span>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <div className="flex-1 relative">
                        <Tag className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="添加自定义标签"
                          value={formData.customTag}
                          onChange={(e) => setFormData({ ...formData, customTag: e.target.value })}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault()
                              addTag(formData.customTag)
                            }
                          }}
                          className="pl-8 border border-input/30 bg-background/50"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => addTag(formData.customTag)}
                        className="cursor-pointer"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        添加
                      </Button>
                    </div>
                    
                    <div className="mt-3">
                      <Label className="text-sm text-muted-foreground mb-2 block">常用标签:</Label>
                      <div className="flex flex-wrap gap-2">
                        {commonTags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className={`cursor-pointer hover:bg-muted ${
                              formData.tags.includes(tag) ? "bg-primary/10" : "bg-muted/40"
                            }`}
                            onClick={() => {
                              if (formData.tags.includes(tag)) {
                                removeTag(tag)
                              } else {
                                addTag(tag)
                              }
                            }}
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between space-x-2 pt-2">
                    <div className="space-y-0.5">
                      <Label htmlFor="project-private">项目可见性</Label>
                      <div className="text-sm text-muted-foreground flex items-center gap-1">
                        <Info className="h-3.5 w-3.5" />
                        {formData.isPrivate ? "仅团队成员可见" : "所有人可见"}
                      </div>
                    </div>
                    <Switch
                      id="project-private"
                      checked={formData.isPrivate}
                      onCheckedChange={(checked) => setFormData({ ...formData, isPrivate: checked })}
                    />
                  </div>
                </CardContent>
              </Card>
              
              {/* 提交按钮区域 */}
              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/projects")}
                  className="cursor-pointer"
                  disabled={isSubmitting}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  className="shadow-sm cursor-pointer"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                      创建中...
                    </>
                  ) : (
                    <>创建项目</>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </main>
      </div>
    </div>
  )
} 